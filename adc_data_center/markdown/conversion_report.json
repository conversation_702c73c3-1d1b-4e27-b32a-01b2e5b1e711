{"timestamp": "2025-08-08T10:56:46.399106", "total_xml_files": 88, "successful_conversions": 88, "failed_conversions": 0, "conversion_details": [{"work_id": "W3195418414", "xml_file": "W3195418414.xml", "md_file": "w3195418414.md", "status": "success"}, {"work_id": "W2994610571", "xml_file": "W2994610571.xml", "md_file": "w2994610571.md", "status": "success"}, {"work_id": "W4396804283", "xml_file": "W4396804283.xml", "md_file": "w4396804283.md", "status": "success"}, {"work_id": "W4400522799", "xml_file": "W4400522799.xml", "md_file": "w4400522799.md", "status": "success"}, {"work_id": "W4393015437", "xml_file": "W4393015437.xml", "md_file": "w4393015437.md", "status": "success"}, {"work_id": "W2891141728", "xml_file": "W2891141728.xml", "md_file": "w2891141728.md", "status": "success"}, {"work_id": "W3152793228", "xml_file": "W3152793228.xml", "md_file": "w3152793228.md", "status": "success"}, {"work_id": "W1491690127", "xml_file": "W1491690127.xml", "md_file": "w1491690127.md", "status": "success"}, {"work_id": "W4396807179", "xml_file": "W4396807179.xml", "md_file": "w4396807179.md", "status": "success"}, {"work_id": "W3182715110", "xml_file": "W3182715110.xml", "md_file": "w3182715110.md", "status": "success"}, {"work_id": "W4394615828", "xml_file": "W4394615828.xml", "md_file": "w4394615828.md", "status": "success"}, {"work_id": "W4390577977", "xml_file": "W4390577977.xml", "md_file": "w4390577977.md", "status": "success"}, {"work_id": "W1670498114", "xml_file": "W1670498114.xml", "md_file": "w1670498114.md", "status": "success"}, {"work_id": "W4401077310", "xml_file": "W4401077310.xml", "md_file": "w4401077310.md", "status": "success"}, {"work_id": "W2597720565", "xml_file": "W2597720565.xml", "md_file": "w2597720565.md", "status": "success"}, {"work_id": "W4392047826", "xml_file": "W4392047826.xml", "md_file": "w4392047826.md", "status": "success"}, {"work_id": "W4312117329", "xml_file": "W4312117329.xml", "md_file": "w4312117329.md", "status": "success"}, {"work_id": "W2800788936", "xml_file": "W2800788936.xml", "md_file": "w2800788936.md", "status": "success"}, {"work_id": "W4390885493", "xml_file": "W4390885493.xml", "md_file": "w4390885493.md", "status": "success"}, {"work_id": "W4362561157", "xml_file": "W4362561157.xml", "md_file": "w4362561157.md", "status": "success"}, {"work_id": "W4386484293", "xml_file": "W4386484293.xml", "md_file": "w4386484293.md", "status": "success"}, {"work_id": "W3167756601", "xml_file": "W3167756601.xml", "md_file": "w3167756601.md", "status": "success"}, {"work_id": "W2150244588", "xml_file": "W2150244588.xml", "md_file": "w2150244588.md", "status": "success"}, {"work_id": "W4392158917", "xml_file": "W4392158917.xml", "md_file": "w4392158917.md", "status": "success"}, {"work_id": "W4385617285", "xml_file": "W4385617285.xml", "md_file": "w4385617285.md", "status": "success"}, {"work_id": "W2894199580", "xml_file": "W2894199580.xml", "md_file": "w2894199580.md", "status": "success"}, {"work_id": "W3135170551", "xml_file": "W3135170551.xml", "md_file": "w3135170551.md", "status": "success"}, {"work_id": "W2767645122", "xml_file": "W2767645122.xml", "md_file": "w2767645122.md", "status": "success"}, {"work_id": "W4291202047", "xml_file": "W4291202047.xml", "md_file": "w4291202047.md", "status": "success"}, {"work_id": "W3201179913", "xml_file": "W3201179913.xml", "md_file": "w3201179913.md", "status": "success"}, {"work_id": "W4388422409", "xml_file": "W4388422409.xml", "md_file": "w4388422409.md", "status": "success"}, {"work_id": "W4210421364", "xml_file": "W4210421364.xml", "md_file": "w4210421364.md", "status": "success"}, {"work_id": "W4220785729", "xml_file": "W4220785729.xml", "md_file": "w4220785729.md", "status": "success"}, {"work_id": "W4283165626", "xml_file": "W4283165626.xml", "md_file": "w4283165626.md", "status": "success"}, {"work_id": "W2993509409", "xml_file": "W2993509409.xml", "md_file": "w2993509409.md", "status": "success"}, {"work_id": "W4406661909", "xml_file": "W4406661909.xml", "md_file": "w4406661909.md", "status": "success"}, {"work_id": "W4396659145", "xml_file": "W4396659145.xml", "md_file": "w4396659145.md", "status": "success"}, {"work_id": "W2907078722", "xml_file": "W2907078722.xml", "md_file": "w2907078722.md", "status": "success"}, {"work_id": "W2911746982", "xml_file": "W2911746982.xml", "md_file": "w2911746982.md", "status": "success"}, {"work_id": "W2923147053", "xml_file": "W2923147053.xml", "md_file": "w2923147053.md", "status": "success"}, {"work_id": "W4400235539", "xml_file": "W4400235539.xml", "md_file": "w4400235539.md", "status": "success"}, {"work_id": "W4226170358", "xml_file": "W4226170358.xml", "md_file": "w4226170358.md", "status": "success"}, {"work_id": "W2767336874", "xml_file": "W2767336874.xml", "md_file": "w2767336874.md", "status": "success"}, {"work_id": "W4390743855", "xml_file": "W4390743855.xml", "md_file": "w4390743855.md", "status": "success"}, {"work_id": "W4316814847", "xml_file": "W4316814847.xml", "md_file": "w4316814847.md", "status": "success"}, {"work_id": "W4226097966", "xml_file": "W4226097966.xml", "md_file": "w4226097966.md", "status": "success"}, {"work_id": "W2949179770", "xml_file": "W2949179770.xml", "md_file": "w2949179770.md", "status": "success"}, {"work_id": "W4225380120", "xml_file": "W4225380120.xml", "md_file": "w4225380120.md", "status": "success"}, {"work_id": "W2982651299", "xml_file": "W2982651299.xml", "md_file": "w2982651299.md", "status": "success"}, {"work_id": "W2088635881", "xml_file": "W2088635881.xml", "md_file": "w2088635881.md", "status": "success"}, {"work_id": "W3157341523", "xml_file": "W3157341523.xml", "md_file": "w3157341523.md", "status": "success"}, {"work_id": "W4391224140", "xml_file": "W4391224140.xml", "md_file": "w4391224140.md", "status": "success"}, {"work_id": "W4200625287", "xml_file": "W4200625287.xml", "md_file": "w4200625287.md", "status": "success"}, {"work_id": "W1983386052", "xml_file": "W1983386052.xml", "md_file": "w1983386052.md", "status": "success"}, {"work_id": "W3099316220", "xml_file": "W3099316220.xml", "md_file": "w3099316220.md", "status": "success"}, {"work_id": "W2034382557", "xml_file": "W2034382557.xml", "md_file": "w2034382557.md", "status": "success"}, {"work_id": "W4392466679", "xml_file": "W4392466679.xml", "md_file": "w4392466679.md", "status": "success"}, {"work_id": "W4381943608", "xml_file": "W4381943608.xml", "md_file": "w4381943608.md", "status": "success"}, {"work_id": "W4293149970", "xml_file": "W4293149970.xml", "md_file": "w4293149970.md", "status": "success"}, {"work_id": "W4283659311", "xml_file": "W4283659311.xml", "md_file": "w4283659311.md", "status": "success"}, {"work_id": "W4388771441", "xml_file": "W4388771441.xml", "md_file": "w4388771441.md", "status": "success"}, {"work_id": "W2755285110", "xml_file": "W2755285110.xml", "md_file": "w2755285110.md", "status": "success"}, {"work_id": "W2071163127", "xml_file": "W2071163127.xml", "md_file": "w2071163127.md", "status": "success"}, {"work_id": "W4315631613", "xml_file": "W4315631613.xml", "md_file": "w4315631613.md", "status": "success"}, {"work_id": "W3083782960", "xml_file": "W3083782960.xml", "md_file": "w3083782960.md", "status": "success"}, {"work_id": "W3033351565", "xml_file": "W3033351565.xml", "md_file": "w3033351565.md", "status": "success"}, {"work_id": "W4376959447", "xml_file": "W4376959447.xml", "md_file": "w4376959447.md", "status": "success"}, {"work_id": "W4392643092", "xml_file": "W4392643092.xml", "md_file": "w4392643092.md", "status": "success"}, {"work_id": "W4391838941", "xml_file": "W4391838941.xml", "md_file": "w4391838941.md", "status": "success"}, {"work_id": "W4387939270", "xml_file": "W4387939270.xml", "md_file": "w4387939270.md", "status": "success"}, {"work_id": "W4394883394", "xml_file": "W4394883394.xml", "md_file": "w4394883394.md", "status": "success"}, {"work_id": "W4386861187", "xml_file": "W4386861187.xml", "md_file": "w4386861187.md", "status": "success"}, {"work_id": "W2987115036", "xml_file": "W2987115036.xml", "md_file": "w2987115036.md", "status": "success"}, {"work_id": "W3177941998", "xml_file": "W3177941998.xml", "md_file": "w3177941998.md", "status": "success"}, {"work_id": "W4387189752", "xml_file": "W4387189752.xml", "md_file": "w4387189752.md", "status": "success"}, {"work_id": "W3043062793", "xml_file": "W3043062793.xml", "md_file": "w3043062793.md", "status": "success"}, {"work_id": "W2059265067", "xml_file": "W2059265067.xml", "md_file": "w2059265067.md", "status": "success"}, {"work_id": "W4390615633", "xml_file": "W4390615633.xml", "md_file": "w4390615633.md", "status": "success"}, {"work_id": "W4395049238", "xml_file": "W4395049238.xml", "md_file": "w4395049238.md", "status": "success"}, {"work_id": "W4391276612", "xml_file": "W4391276612.xml", "md_file": "w4391276612.md", "status": "success"}, {"work_id": "W2886837533", "xml_file": "W2886837533.xml", "md_file": "w2886837533.md", "status": "success"}, {"work_id": "W4398201134", "xml_file": "W4398201134.xml", "md_file": "w4398201134.md", "status": "success"}, {"work_id": "W4393229268", "xml_file": "W4393229268.xml", "md_file": "w4393229268.md", "status": "success"}, {"work_id": "W2913433668", "xml_file": "W2913433668.xml", "md_file": "w2913433668.md", "status": "success"}, {"work_id": "W4390739402", "xml_file": "W4390739402.xml", "md_file": "w4390739402.md", "status": "success"}, {"work_id": "W2898306454", "xml_file": "W2898306454.xml", "md_file": "w2898306454.md", "status": "success"}, {"work_id": "W4394953855", "xml_file": "W4394953855.xml", "md_file": "w4394953855.md", "status": "success"}, {"work_id": "W4389113389", "xml_file": "W4389113389.xml", "md_file": "w4389113389.md", "status": "success"}], "failed_files": []}